package com.concise.common.consts;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;

/**
 * SpringSecurity相关常量
 *
 * <AUTHOR>
 * @date 2020/3/18 17:49
 */
@Configurable
public class SpringSecurityConstant {

    @Value("${spring.profiles.active}")
    private String active;


    /**
     * 放开权限校验的接口
     */
    public static String[] NONE_SECURITY_URL_PATTERNS = {

            //前端的
            "/favicon.ico",

            //后端的
            "/login",
            "/logout",
            "/oauth/**",
            "/loginQrCode",

            //文件的
            "/sysFileInfo/upload",
            "/sysFileInfo/download",
            "/sysFileInfo/preview",
            "/sysDemo/pic",

            //druid的
            "/druid/**",

            //获取验证码
            "/captcha/**",
            "/getCaptchaOpen",
            //统一用户
            "/unifyUser/**",
            //综管单点
            "/loginSso",
            //浙里办单点
            "/wechatLogin",
            //无权限用户注册
            "/resUserManage/registeredUser",
            //无权限用户机构树
            "/resOrgManage/regTree",
            //法律明白人
            "/lawUnderstandPeople/public/**",
            //临时放开绑定业务
            "/sysFileInfo/bindBusiness",
            "/bigScreen/metrics",
            "/bigScreen/lawPosition/metrics",
            "/bigScreen/lawPosition/notice",

            //公共普法资源API
            "/public/resource/**",

            //资源文件
            "/dirOss/file/**",

            //群众端-民主法治村
            "/v2/bigScreen/villageList",
            "/v2/bigScreen/villageTotal",
            "/v2/bigScreen/lawPosition/getDefault",
            "/v2/bigScreen/villageSearch",
            //群众端-法治文化基地
            "/v2/bigScreen/cultureList",
            "/v2/bigScreen/cultureTotal",
            "/v2/bigScreen/cultureDetail",
            "/v2/bigScreen/cultureSearch",
            //群众端-法治文化
            "/v2/bigScreen/lawArt",
            "/v2/bigScreen/lawArtPage",
            "/v2/bigScreen/lawArtCategory",
            "/v2/bigScreen/lawArtCategoryPage",
            "/v2/bigScreen/lawArtGroup",

    };

    public static String[] SWAGGER_URL = {
            "/doc.html",
            "/webjars/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v2/api-docs-ext",
            "/configuration/ui",
            "/configuration/security"
    };

}
